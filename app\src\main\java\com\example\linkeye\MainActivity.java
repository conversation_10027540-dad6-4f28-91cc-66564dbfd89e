package com.example.linkeye;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.GridLayout;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;

public class MainActivity extends Activity {

    private static final String PREFS = "tv_prefs";
    private static final String KEY_GRID = "grid"; // 1/2/4
    private static final String KEY_PAGE = "page";
    private static final String KEY_ASSIGN_PREFIX = "assign_"; // assign_0,1,2,3 -> camera index or -1
    private static final String KEY_MUTE_PREFIX = "mute_"; // mute_0,1,2,3 -> bool

    private GridLayout grid;
    private final List<PlayerTileView> tiles = new ArrayList<>();

    private List<CameraInfo> cameras = new ArrayList<>();
    private int gridMode = 4; // 1,2,4
    private int page = 0; // for 4-grid

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Load IJK libraries once
        try {
            IjkMediaPlayer.loadLibrariesOnce(null);
            IjkMediaPlayer.native_profileBegin("libijkplayer.so");
        } catch (UnsatisfiedLinkError e) {
            Toast.makeText(this, "Failed to load ijkplayer libs" + e.getMessage(), Toast.LENGTH_LONG).show();
        }

        grid = (GridLayout) findViewById(R.id.grid_container);

        cameras = CameraRepository.load(this);
        restoreState();
        buildGrid();
        assignInitialCameras();
    }

    private void buildGrid() {
        grid.removeAllViews();
        tiles.clear();
        int columns;
        int rows;
        if (gridMode == 1) { columns = 1; rows = 1; }
        else if (gridMode == 2) { columns = 2; rows = 1; }
        else { columns = 2; rows = 2; }
        grid.setColumnCount(columns);
        grid.setRowCount(rows);

        int tileCount = gridMode;
        for (int i = 0; i < tileCount; i++) {
            PlayerTileView tile = new PlayerTileView(this);
            tile.setId(View.generateViewId()); // Generate unique ID for focus navigation
            GridLayout.LayoutParams lp = new GridLayout.LayoutParams();
            lp.width = 0;
            lp.height = 0;
            lp.columnSpec = GridLayout.spec(i % columns, 1f);
            lp.rowSpec = GridLayout.spec(i / columns, 1f);
            // Add small margins between tiles
            int margin = 2; // 2dp margin
            lp.setMargins(margin, margin, margin, margin);
            tile.setLayoutParams(lp);
            final int idx = i;
            tile.setOnLongClickListener(v -> {
                showTileMenu(idx);
                return true;
            });
            tiles.add(tile);
            grid.addView(tile);
        }

        // Set up focus navigation between tiles
        setupFocusNavigation();

        // Request focus on the first tile after a short delay to ensure layout is complete
        if (!tiles.isEmpty()) {
            tiles.get(0).post(() -> tiles.get(0).requestFocus());
        }
    }

    private void setupFocusNavigation() {
        if (tiles.isEmpty()) return;

        int columns = (gridMode == 1) ? 1 : 2;
        int rows = (gridMode == 1) ? 1 : (gridMode == 2) ? 1 : 2;

        for (int i = 0; i < tiles.size(); i++) {
            PlayerTileView tile = tiles.get(i);
            int row = i / columns;
            int col = i % columns;

            // Clear any existing focus navigation first
            tile.setNextFocusUpId(View.NO_ID);
            tile.setNextFocusDownId(View.NO_ID);
            tile.setNextFocusLeftId(View.NO_ID);
            tile.setNextFocusRightId(View.NO_ID);

            // Set up directional navigation
            // Up
            int upIndex = ((row - 1 + rows) % rows) * columns + col;
            if (upIndex < tiles.size() && upIndex != i) {
                tile.setNextFocusUpId(tiles.get(upIndex).getId());
            }

            // Down
            int downIndex = ((row + 1) % rows) * columns + col;
            if (downIndex < tiles.size() && downIndex != i) {
                tile.setNextFocusDownId(tiles.get(downIndex).getId());
            }

            // Left
            int leftIndex = row * columns + ((col - 1 + columns) % columns);
            if (leftIndex < tiles.size() && leftIndex != i) {
                tile.setNextFocusLeftId(tiles.get(leftIndex).getId());
            }

            // Right
            int rightIndex = row * columns + ((col + 1) % columns);
            if (rightIndex < tiles.size() && rightIndex != i) {
                tile.setNextFocusRightId(tiles.get(rightIndex).getId());
            }
        }
    }

    private void assignInitialCameras() {
        // Pull current assignments from prefs, or default sequential by page
        SharedPreferences sp = getSharedPreferences(PREFS, MODE_PRIVATE);
        Set<Integer> used = new HashSet<>();
        for (int i = 0; i < gridMode; i++) {
            int camIndex = sp.getInt(KEY_ASSIGN_PREFIX + i, -2);
            boolean prefExists = sp.contains(KEY_ASSIGN_PREFIX + i);
            if (!prefExists) {
                // default assignment based on page
                int globalIndex = page * 4 + i;
                camIndex = (globalIndex >= 0 && globalIndex < cameras.size()) ? globalIndex : -1;
            }
            CameraInfo assigned = (camIndex >= 0 && camIndex < cameras.size()) ? cameras.get(camIndex) : null;
            PlayerTileView tile = tiles.get(i);
            tile.assignCamera(assigned);
            boolean muted = sp.getBoolean(KEY_MUTE_PREFIX + i, assigned != null && assigned.defaultMuted);
            tile.setMuted(muted);
            if (camIndex >= 0) used.add(camIndex);
        }
    }

    private AlertDialog currentMenuDialog = null;

    private void showTileMenu(int tileIndex) {
        // Dismiss any existing dialog first
        if (currentMenuDialog != null && currentMenuDialog.isShowing()) {
            currentMenuDialog.dismiss();
            currentMenuDialog = null;
        }

        String[] items = new String[]{"选择摄像头", "静音开关"};
        currentMenuDialog = new AlertDialog.Builder(this)
                .setTitle("设置")
                .setItems(items, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (which == 0) {
                            // Dismiss and clear reference immediately
                            dialog.dismiss();
                            currentMenuDialog = null;
                            // Show camera picker after a short delay
                            grid.postDelayed(() -> showCameraPicker(tileIndex), 150);
                        } else if (which == 1) {
                            // Dismiss and clear reference immediately
                            dialog.dismiss();
                            currentMenuDialog = null;
                            toggleMute(tileIndex);
                        }
                    }
                })
                .setOnDismissListener(dialog -> {
                    // Clear reference when dialog is dismissed
                    currentMenuDialog = null;
                })
                .create();
        currentMenuDialog.show();
    }

    private void toggleMute(int tileIndex) {
        if (tileIndex < 0 || tileIndex >= tiles.size()) return;
        PlayerTileView tile = tiles.get(tileIndex);
        tile.setMuted(!tile.isMuted());
        persistAssignments();

        // Restore focus to the tile after mute toggle
        tile.post(() -> tile.requestFocus());
    }

    private void showCameraPicker(int tileIndex) {
        // Make sure any existing menu dialog is dismissed
        if (currentMenuDialog != null && currentMenuDialog.isShowing()) {
            currentMenuDialog.dismiss();
            currentMenuDialog = null;
        }

        List<String> names = new ArrayList<>();
        names.add("置空");
        for (CameraInfo c : cameras) names.add(c.name);
        AlertDialog cameraDialog = new AlertDialog.Builder(this)
                .setTitle("选择摄像头")
                .setItems(names.toArray(new String[0]), (dialog, which) -> {
                    // Always dismiss the dialog first
                    dialog.dismiss();

                    if (which == 0) {
                        setTileCamera(tileIndex, -1);
                    } else {
                        int camIndex = which - 1;
                        setTileCamera(tileIndex, camIndex);
                    }
                })
                .create();
        cameraDialog.show();
    }

    private void setTileCamera(int tileIndex, int cameraIndex) {
        if (tileIndex < 0 || tileIndex >= tiles.size()) return;
        // If selected camera already used in other tile, clear the old tile
        if (cameraIndex >= 0) {
            for (int i = 0; i < tiles.size(); i++) {
                if (i == tileIndex) continue;
                CameraInfo other = tiles.get(i).getAssignedCamera();
                if (other != null && cameras.indexOf(other) == cameraIndex) {
                    tiles.get(i).assignCamera(null);
                }
            }
            CameraInfo assign = (cameraIndex < cameras.size()) ? cameras.get(cameraIndex) : null;
            tiles.get(tileIndex).assignCamera(assign);
            tiles.get(tileIndex).setMuted(assign != null && assign.defaultMuted);
        } else {
            tiles.get(tileIndex).assignCamera(null);
        }
        persistAssignments();

        // Restore focus to the tile after camera assignment
        tiles.get(tileIndex).post(() -> tiles.get(tileIndex).requestFocus());
    }

    private void persistAssignments() {
        SharedPreferences sp = getSharedPreferences(PREFS, MODE_PRIVATE);
        SharedPreferences.Editor ed = sp.edit();
        ed.putInt(KEY_GRID, gridMode);
        ed.putInt(KEY_PAGE, page);
        for (int i = 0; i < tiles.size(); i++) {
            PlayerTileView t = tiles.get(i);
            int assignIndex = -1;
            CameraInfo c = t.getAssignedCamera();
            if (c != null) assignIndex = cameras.indexOf(c);
            ed.putInt(KEY_ASSIGN_PREFIX + i, assignIndex);
            ed.putBoolean(KEY_MUTE_PREFIX + i, t.isMuted());
        }
        ed.apply();
    }

    private void restoreState() {
        SharedPreferences sp = getSharedPreferences(PREFS, MODE_PRIVATE);
        gridMode = sp.getInt(KEY_GRID, 4);
        if (gridMode != 1 && gridMode != 2 && gridMode != 4) gridMode = 4;
        page = sp.getInt(KEY_PAGE, 0);
    }

    private void chooseGridMode() {
        String[] items = new String[]{"1路", "2路", "4路"};
        new AlertDialog.Builder(this)
                .setTitle("画面数")
                .setItems(items, (dialog, which) -> {
                    int newMode = (which == 0 ? 1 : which == 1 ? 2 : 4);
                    if (newMode != gridMode) {
                        gridMode = newMode;
                        page = 0;
                        buildGrid();
                        assignInitialCameras();
                        persistAssignments();
                    }
                })
                .show();
    }

    private void pageNext() {
        if (cameras.isEmpty()) return;
        if (gridMode == 4) {
            int pages = (cameras.size() + 3) / 4;
            if (pages <= 1) return;
            page = (page + 1) % pages;
            assignInitialCameras();
            persistAssignments();
        }
    }

    private void pagePrev() {
        if (cameras.isEmpty()) return;
        if (gridMode == 4) {
            int pages = (cameras.size() + 3) / 4;
            if (pages <= 1) return;
            page = (page - 1 + pages) % pages;
            assignInitialCameras();
            persistAssignments();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        // MENU to choose grid
        if (keyCode == KeyEvent.KEYCODE_MENU) {
            chooseGridMode();
            return true;
        }

        // CENTER/ENTER/OK for tile menu
        if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_ENTER || keyCode == KeyEvent.KEYCODE_BUTTON_A) {
            View focused = getCurrentFocus();
            if (focused instanceof PlayerTileView) {
                int tileIndex = tiles.indexOf(focused);
                if (tileIndex >= 0) {
                    showTileMenu(tileIndex);
                    return true;
                }
            }
        }

        // BACK key handling
        if (keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_BUTTON_B) {
            // Let system handle back key (exit app)
            return super.onKeyDown(keyCode, event);
        }

        // CHANNEL/PAGE/MEDIA keys for paging when 4-grid
        if (gridMode == 4) {
            if (keyCode == KeyEvent.KEYCODE_CHANNEL_UP || keyCode == KeyEvent.KEYCODE_PAGE_UP || keyCode == KeyEvent.KEYCODE_MEDIA_NEXT) {
                pageNext();
                return true;
            }
            if (keyCode == KeyEvent.KEYCODE_CHANNEL_DOWN || keyCode == KeyEvent.KEYCODE_PAGE_DOWN || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS) {
                pagePrev();
                return true;
            }
        }

        // Let system handle directional navigation (DPAD_UP, DPAD_DOWN, DPAD_LEFT, DPAD_RIGHT)
        // This is handled automatically by the focus system
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onStop() {
        super.onStop();
        for (PlayerTileView t : tiles) t.release();
        persistAssignments();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        IjkMediaPlayer.native_profileEnd();
    }
}
