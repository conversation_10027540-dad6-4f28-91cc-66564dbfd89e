1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.linkeye"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
12
13    <uses-feature
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-87
14        android:name="android.software.leanback"
14-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:19-59
15        android:required="false" />
15-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:60-84
16    <uses-feature
16-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-90
17        android:name="android.hardware.touchscreen"
17-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:19-62
18        android:required="false" />
18-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:63-87
19
20    <application
20-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-23:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:9-35
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:hardwareAccelerated="true"
24-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-43
25        android:label="link-eye"
25-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:10:9-33
26        android:testOnly="true" >
27        <activity
27-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-22:20
28            android:name="com.example.linkeye.MainActivity"
28-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:13-41
29            android:configChanges="keyboardHidden|orientation|screenSize"
29-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:13-74
30            android:exported="true" >
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:13-36
31            <intent-filter>
31-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:13-21:29
32                <action android:name="android.intent.action.MAIN" />
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:17-69
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:17-77
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:27-74
35                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:17-86
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:27-83
36            </intent-filter>
37        </activity>
38    </application>
39
40</manifest>
