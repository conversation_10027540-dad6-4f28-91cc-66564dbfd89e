<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:id="@+id/tile_root"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:foregroundGravity="center"
	android:focusable="true"
	android:focusableInTouchMode="false"
	android:clickable="true"
	android:longClickable="true">

	<SurfaceView
		android:id="@+id/surface_view"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:layout_gravity="center" />

	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:layout_gravity="top"
		android:orientation="horizontal"
		android:padding="8dp"
		android:background="#66000000">
		<TextView
			android:id="@+id/title"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:textColor="#FFFFFF"
			android:textSize="14sp"
			android:text="Cam" />
		<ImageView
			android:id="@+id/mute_badge"
			android:layout_width="20dp"
			android:layout_height="20dp"
			android:src="@drawable/ic_mute"
			android:layout_marginStart="8dp"
			android:visibility="gone"/>
	</LinearLayout>

	<View
		android:id="@+id/focus_border"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@android:color/transparent"/>

</FrameLayout>
